{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 16002026299222347435, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-a429100abb811376\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}