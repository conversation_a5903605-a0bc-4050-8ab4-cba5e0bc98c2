// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.11.1.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'api/simple.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'frb_generated.io.dart'
    if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Main entrypoint of the Rust API
class RustLib extends BaseEntrypoint<RustLibApi, RustLibApiImpl, RustLibWire> {
  @internal
  static final instance = RustLib._();

  RustLib._();

  /// Initialize flutter_rust_bridge
  static Future<void> init({
    RustLibApi? api,
    BaseHandler? handler,
    ExternalLibrary? externalLibrary,
    bool forceSameCodegenVersion = true,
  }) async {
    await instance.initImpl(
      api: api,
      handler: handler,
      externalLibrary: externalLibrary,
      forceSameCodegenVersion: forceSameCodegenVersion,
    );
  }

  /// Initialize flutter_rust_bridge in mock mode.
  /// No libraries for FFI are loaded.
  static void initMock({required RustLibApi api}) {
    instance.initMockImpl(api: api);
  }

  /// Dispose flutter_rust_bridge
  ///
  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
  /// is automatically disposed when the app stops.
  static void dispose() => instance.disposeImpl();

  @override
  ApiImplConstructor<RustLibApiImpl, RustLibWire> get apiImplConstructor =>
      RustLibApiImpl.new;

  @override
  WireConstructor<RustLibWire> get wireConstructor =>
      RustLibWire.fromExternalLibrary;

  @override
  Future<void> executeRustInitializers() async {
    await api.crateApiSimpleInitApp();
  }

  @override
  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig =>
      kDefaultExternalLibraryLoaderConfig;

  @override
  String get codegenVersion => '2.11.1';

  @override
  int get rustContentHash => -62903975;

  static const kDefaultExternalLibraryLoaderConfig =
      ExternalLibraryLoaderConfig(
        stem: 'flutter_plugin_rs_rust',
        ioDirectory: 'rust/target/release/',
        webPrefix: 'pkg/',
      );
}

abstract class RustLibApi extends BaseApi {
  int crateApiSimpleFlutterRsAutoAccessorGetBaudRate({required FlutterRs that});

  String crateApiSimpleFlutterRsAutoAccessorGetPortName({
    required FlutterRs that,
  });

  void crateApiSimpleFlutterRsAutoAccessorSetBaudRate({
    required FlutterRs that,
    required int baudRate,
  });

  void crateApiSimpleFlutterRsAutoAccessorSetPortName({
    required FlutterRs that,
    required String portName,
  });

  Future<void> crateApiSimpleFlutterRsClose({required FlutterRs that});

  Future<bool> crateApiSimpleFlutterRsIsConnected({required FlutterRs that});

  FlutterRs crateApiSimpleFlutterRsNew({
    required String portName,
    required int baudRate,
  });

  Future<void> crateApiSimpleFlutterRsOpen({required FlutterRs that});

  Future<Uint8List> crateApiSimpleFlutterRsRead({required FlutterRs that});

  Future<BigInt> crateApiSimpleFlutterRsWrite({
    required FlutterRs that,
    required List<int> output,
  });

  String crateApiSimpleGreet({required String name});

  Future<void> crateApiSimpleInitApp();

  Future<String> crateApiSimpleMyStructGreet({required MyStruct that});

  MyStruct crateApiSimpleMyStructNew({required String name, required int age});

  RustArcIncrementStrongCountFnType
  get rust_arc_increment_strong_count_FlutterRs;

  RustArcDecrementStrongCountFnType
  get rust_arc_decrement_strong_count_FlutterRs;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_FlutterRsPtr;
}

class RustLibApiImpl extends RustLibApiImplPlatform implements RustLibApi {
  RustLibApiImpl({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  @override
  int crateApiSimpleFlutterRsAutoAccessorGetBaudRate({
    required FlutterRs that,
  }) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 1)!;
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_u_32,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleFlutterRsAutoAccessorGetBaudRateConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsAutoAccessorGetBaudRateConstMeta =>
      const TaskConstMeta(
        debugName: "FlutterRs_auto_accessor_get_baud_rate",
        argNames: ["that"],
      );

  @override
  String crateApiSimpleFlutterRsAutoAccessorGetPortName({
    required FlutterRs that,
  }) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 2)!;
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_String,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleFlutterRsAutoAccessorGetPortNameConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsAutoAccessorGetPortNameConstMeta =>
      const TaskConstMeta(
        debugName: "FlutterRs_auto_accessor_get_port_name",
        argNames: ["that"],
      );

  @override
  void crateApiSimpleFlutterRsAutoAccessorSetBaudRate({
    required FlutterRs that,
    required int baudRate,
  }) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          sse_encode_u_32(baudRate, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 3)!;
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_unit,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleFlutterRsAutoAccessorSetBaudRateConstMeta,
        argValues: [that, baudRate],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsAutoAccessorSetBaudRateConstMeta =>
      const TaskConstMeta(
        debugName: "FlutterRs_auto_accessor_set_baud_rate",
        argNames: ["that", "baudRate"],
      );

  @override
  void crateApiSimpleFlutterRsAutoAccessorSetPortName({
    required FlutterRs that,
    required String portName,
  }) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          sse_encode_String(portName, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 4)!;
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_unit,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleFlutterRsAutoAccessorSetPortNameConstMeta,
        argValues: [that, portName],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsAutoAccessorSetPortNameConstMeta =>
      const TaskConstMeta(
        debugName: "FlutterRs_auto_accessor_set_port_name",
        argNames: ["that", "portName"],
      );

  @override
  Future<void> crateApiSimpleFlutterRsClose({required FlutterRs that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 5,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_unit,
          decodeErrorData: sse_decode_AnyhowException,
        ),
        constMeta: kCrateApiSimpleFlutterRsCloseConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsCloseConstMeta =>
      const TaskConstMeta(debugName: "FlutterRs_close", argNames: ["that"]);

  @override
  Future<bool> crateApiSimpleFlutterRsIsConnected({required FlutterRs that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 6,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_bool,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleFlutterRsIsConnectedConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsIsConnectedConstMeta =>
      const TaskConstMeta(
        debugName: "FlutterRs_is_connected",
        argNames: ["that"],
      );

  @override
  FlutterRs crateApiSimpleFlutterRsNew({
    required String portName,
    required int baudRate,
  }) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_String(portName, serializer);
          sse_encode_u_32(baudRate, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 7)!;
        },
        codec: SseCodec(
          decodeSuccessData:
              sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleFlutterRsNewConstMeta,
        argValues: [portName, baudRate],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsNewConstMeta => const TaskConstMeta(
    debugName: "FlutterRs_new",
    argNames: ["portName", "baudRate"],
  );

  @override
  Future<void> crateApiSimpleFlutterRsOpen({required FlutterRs that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 8,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_unit,
          decodeErrorData: sse_decode_AnyhowException,
        ),
        constMeta: kCrateApiSimpleFlutterRsOpenConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsOpenConstMeta =>
      const TaskConstMeta(debugName: "FlutterRs_open", argNames: ["that"]);

  @override
  Future<Uint8List> crateApiSimpleFlutterRsRead({required FlutterRs that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 9,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_list_prim_u_8_strict,
          decodeErrorData: sse_decode_AnyhowException,
        ),
        constMeta: kCrateApiSimpleFlutterRsReadConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsReadConstMeta =>
      const TaskConstMeta(debugName: "FlutterRs_read", argNames: ["that"]);

  @override
  Future<BigInt> crateApiSimpleFlutterRsWrite({
    required FlutterRs that,
    required List<int> output,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
            that,
            serializer,
          );
          sse_encode_list_prim_u_8_loose(output, serializer);
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 10,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_usize,
          decodeErrorData: sse_decode_AnyhowException,
        ),
        constMeta: kCrateApiSimpleFlutterRsWriteConstMeta,
        argValues: [that, output],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleFlutterRsWriteConstMeta =>
      const TaskConstMeta(
        debugName: "FlutterRs_write",
        argNames: ["that", "output"],
      );

  @override
  String crateApiSimpleGreet({required String name}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_String(name, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 11)!;
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_String,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleGreetConstMeta,
        argValues: [name],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleGreetConstMeta =>
      const TaskConstMeta(debugName: "greet", argNames: ["name"]);

  @override
  Future<void> crateApiSimpleInitApp() {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 12,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_unit,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleInitAppConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleInitAppConstMeta =>
      const TaskConstMeta(debugName: "init_app", argNames: []);

  @override
  Future<String> crateApiSimpleMyStructGreet({required MyStruct that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_my_struct(that, serializer);
          pdeCallFfi(
            generalizedFrbRustBinding,
            serializer,
            funcId: 13,
            port: port_,
          );
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_String,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleMyStructGreetConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleMyStructGreetConstMeta =>
      const TaskConstMeta(debugName: "my_struct_greet", argNames: ["that"]);

  @override
  MyStruct crateApiSimpleMyStructNew({required String name, required int age}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_String(name, serializer);
          sse_encode_u_32(age, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 14)!;
        },
        codec: SseCodec(
          decodeSuccessData: sse_decode_my_struct,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiSimpleMyStructNewConstMeta,
        argValues: [name, age],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiSimpleMyStructNewConstMeta => const TaskConstMeta(
    debugName: "my_struct_new",
    argNames: ["name", "age"],
  );

  RustArcIncrementStrongCountFnType
  get rust_arc_increment_strong_count_FlutterRs => wire
      .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs;

  RustArcDecrementStrongCountFnType
  get rust_arc_decrement_strong_count_FlutterRs => wire
      .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs;

  @protected
  AnyhowException dco_decode_AnyhowException(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return AnyhowException(raw as String);
  }

  @protected
  FlutterRs
  dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FlutterRsImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  FlutterRs
  dco_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FlutterRsImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  FlutterRs
  dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FlutterRsImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  FlutterRs
  dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FlutterRsImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  String dco_decode_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as String;
  }

  @protected
  bool dco_decode_bool(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as bool;
  }

  @protected
  MyStruct dco_decode_box_autoadd_my_struct(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_my_struct(raw);
  }

  @protected
  List<int> dco_decode_list_prim_u_8_loose(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as List<int>;
  }

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as Uint8List;
  }

  @protected
  MyStruct dco_decode_my_struct(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2)
      throw Exception('unexpected arr length: expect 2 but see ${arr.length}');
    return MyStruct.raw(
      name: dco_decode_String(arr[0]),
      age: dco_decode_u_32(arr[1]),
    );
  }

  @protected
  int dco_decode_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  int dco_decode_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  void dco_decode_unit(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return;
  }

  @protected
  BigInt dco_decode_usize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  AnyhowException sse_decode_AnyhowException(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_String(deserializer);
    return AnyhowException(inner);
  }

  @protected
  FlutterRs
  sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FlutterRsImpl.frbInternalSseDecode(
      sse_decode_usize(deserializer),
      sse_decode_i_32(deserializer),
    );
  }

  @protected
  FlutterRs
  sse_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FlutterRsImpl.frbInternalSseDecode(
      sse_decode_usize(deserializer),
      sse_decode_i_32(deserializer),
    );
  }

  @protected
  FlutterRs
  sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FlutterRsImpl.frbInternalSseDecode(
      sse_decode_usize(deserializer),
      sse_decode_i_32(deserializer),
    );
  }

  @protected
  FlutterRs
  sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FlutterRsImpl.frbInternalSseDecode(
      sse_decode_usize(deserializer),
      sse_decode_i_32(deserializer),
    );
  }

  @protected
  String sse_decode_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return utf8.decoder.convert(inner);
  }

  @protected
  bool sse_decode_bool(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8() != 0;
  }

  @protected
  MyStruct sse_decode_box_autoadd_my_struct(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_my_struct(deserializer));
  }

  @protected
  List<int> sse_decode_list_prim_u_8_loose(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  MyStruct sse_decode_my_struct(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_name = sse_decode_String(deserializer);
    var var_age = sse_decode_u_32(deserializer);
    return MyStruct.raw(name: var_name, age: var_age);
  }

  @protected
  int sse_decode_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint32();
  }

  @protected
  int sse_decode_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8();
  }

  @protected
  void sse_decode_unit(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  int sse_decode_i_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getInt32();
  }

  @protected
  void sse_encode_AnyhowException(
    AnyhowException self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.message, serializer);
  }

  @protected
  void
  sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    FlutterRs self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
      (self as FlutterRsImpl).frbInternalSseEncode(move: true),
      serializer,
    );
  }

  @protected
  void
  sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    FlutterRs self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
      (self as FlutterRsImpl).frbInternalSseEncode(move: false),
      serializer,
    );
  }

  @protected
  void
  sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    FlutterRs self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
      (self as FlutterRsImpl).frbInternalSseEncode(move: false),
      serializer,
    );
  }

  @protected
  void
  sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFlutterRs(
    FlutterRs self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
      (self as FlutterRsImpl).frbInternalSseEncode(move: null),
      serializer,
    );
  }

  @protected
  void sse_encode_String(String self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(utf8.encoder.convert(self), serializer);
  }

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self ? 1 : 0);
  }

  @protected
  void sse_encode_box_autoadd_my_struct(
    MyStruct self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_my_struct(self, serializer);
  }

  @protected
  void sse_encode_list_prim_u_8_loose(
    List<int> self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(
      self is Uint8List ? self : Uint8List.fromList(self),
    );
  }

  @protected
  void sse_encode_list_prim_u_8_strict(
    Uint8List self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self);
  }

  @protected
  void sse_encode_my_struct(MyStruct self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.name, serializer);
    sse_encode_u_32(self.age, serializer);
  }

  @protected
  void sse_encode_u_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint32(self);
  }

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self);
  }

  @protected
  void sse_encode_unit(void self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putInt32(self);
  }
}

@sealed
class FlutterRsImpl extends RustOpaque implements FlutterRs {
  // Not to be used by end users
  FlutterRsImpl.frbInternalDcoDecode(List<dynamic> wire)
    : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  FlutterRsImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
    : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_FlutterRs,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_FlutterRs,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_FlutterRsPtr,
  );

  int get baudRate => RustLib.instance.api
      .crateApiSimpleFlutterRsAutoAccessorGetBaudRate(that: this);

  String get portName => RustLib.instance.api
      .crateApiSimpleFlutterRsAutoAccessorGetPortName(that: this);

  set baudRate(int baudRate) =>
      RustLib.instance.api.crateApiSimpleFlutterRsAutoAccessorSetBaudRate(
        that: this,
        baudRate: baudRate,
      );

  set portName(String portName) =>
      RustLib.instance.api.crateApiSimpleFlutterRsAutoAccessorSetPortName(
        that: this,
        portName: portName,
      );

  Future<void> close() =>
      RustLib.instance.api.crateApiSimpleFlutterRsClose(that: this);

  Future<bool> isConnected() =>
      RustLib.instance.api.crateApiSimpleFlutterRsIsConnected(that: this);

  Future<void> open() =>
      RustLib.instance.api.crateApiSimpleFlutterRsOpen(that: this);

  Future<Uint8List> read() =>
      RustLib.instance.api.crateApiSimpleFlutterRsRead(that: this);

  Future<BigInt> write({required List<int> output}) => RustLib.instance.api
      .crateApiSimpleFlutterRsWrite(that: this, output: output);
}
