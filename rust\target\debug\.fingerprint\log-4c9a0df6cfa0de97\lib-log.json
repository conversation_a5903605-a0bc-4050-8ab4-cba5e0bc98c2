{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"kv_unstable\", \"kv_unstable_serde\", \"kv_unstable_std\", \"kv_unstable_sval\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"serde\", \"std\", \"sval\", \"sval_ref\", \"value-bag\"]", "target": 13251511692805008066, "profile": 2241668132362809309, "path": 7758635249740512970, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\log-4c9a0df6cfa0de97\\dep-lib-log", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}