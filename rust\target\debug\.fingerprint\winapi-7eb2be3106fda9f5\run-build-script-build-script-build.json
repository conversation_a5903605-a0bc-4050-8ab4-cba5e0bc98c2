{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10020888071089587331, "build_script_build", false, 5485419439662281527]], "local": [{"RerunIfChanged": {"output": "debug\\build\\winapi-7eb2be3106fda9f5\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": ["--cfg", "frb_expand"], "config": 0, "compile_kind": 0}