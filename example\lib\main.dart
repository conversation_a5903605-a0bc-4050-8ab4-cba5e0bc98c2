import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_plugin_rs_rust/flutter_plugin_rs_rust.dart';

Future<void> main() async {
  await RustLib.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '串口通信测试',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const SerialPortTestPage(),
    );
  }
}

class SerialPortTestPage extends StatefulWidget {
  const SerialPortTestPage({super.key});

  @override
  State<SerialPortTestPage> createState() => _SerialPortTestPageState();
}

class _SerialPortTestPageState extends State<SerialPortTestPage> {
  // 控制器
  final TextEditingController _portNameController = TextEditingController(text: 'COM1');
  final TextEditingController _inputController = TextEditingController();
  final TextEditingController _outputController = TextEditingController();

  // 串口对象
  FlutterRs? _serialPort;

  // 状态变量
  bool _isConnected = false;
  bool _isConnecting = false;
  Timer? _readTimer;

  @override
  void dispose() {
    _readTimer?.cancel();
    _portNameController.dispose();
    _inputController.dispose();
    _outputController.dispose();
    _closePort();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('串口通信测试'), backgroundColor: Theme.of(context).colorScheme.inversePrimary),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 串口配置区域
            _buildPortConfigSection(),
            const SizedBox(height: 16),

            // 控制按钮区域
            _buildControlButtonsSection(),
            const SizedBox(height: 16),

            // 数据输入区域
            _buildInputSection(),
            const SizedBox(height: 16),

            // 数据输出区域
            _buildOutputSection(),
          ],
        ),
      ),
    );
  }

  // 构建串口配置区域
  Widget _buildPortConfigSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('串口配置', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('串口名称: '),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _portNameController,
                    enabled: !_isConnected && !_isConnecting,
                    decoration: const InputDecoration(
                      hintText: '例如: COM1, /dev/ttyUSB0',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _isConnected ? Icons.circle : Icons.circle_outlined,
                  color: _isConnected ? Colors.green : Colors.grey,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  _isConnected ? '已连接' : '未连接',
                  style: TextStyle(color: _isConnected ? Colors.green : Colors.grey, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建控制按钮区域
  Widget _buildControlButtonsSection() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isConnected || _isConnecting ? null : _openPort,
            icon: _isConnecting
                ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                : const Icon(Icons.link),
            label: Text(_isConnecting ? '连接中...' : '打开串口'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: !_isConnected ? null : _closePort,
            icon: const Icon(Icons.link_off),
            label: const Text('断开'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: !_isConnected || _inputController.text.isEmpty ? null : _sendData,
            icon: const Icon(Icons.send),
            label: const Text('发送'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white),
          ),
        ),
      ],
    );
  }

  // 构建数据输入区域
  Widget _buildInputSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('数据发送', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _inputController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: '输入要发送的数据...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.all(12),
              ),
              onSubmitted: (_) => _sendData(),
            ),
          ],
        ),
      ),
    );
  }

  // 构建数据输出区域
  Widget _buildOutputSection() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('接收数据', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _outputController.clear();
                      });
                    },
                    icon: const Icon(Icons.clear, size: 16),
                    label: const Text('清空'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: TextField(
                  controller: _outputController,
                  maxLines: null,
                  expands: true,
                  readOnly: true,
                  decoration: const InputDecoration(border: OutlineInputBorder(), contentPadding: EdgeInsets.all(12)),
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 打开串口
  Future<void> _openPort() async {
    if (_portNameController.text.trim().isEmpty) {
      _showMessage('请输入串口名称');
      return;
    }

    setState(() {
      _isConnecting = true;
    });

    try {
      _serialPort = FlutterRs(portName: _portNameController.text.trim(), baudRate: 115200);

      await _serialPort!.open();

      setState(() {
        _isConnected = true;
        _isConnecting = false;
      });

      _showMessage('串口连接成功');
      _startReading();
    } catch (e) {
      setState(() {
        _isConnected = false;
        _isConnecting = false;
      });
      _showMessage('串口连接失败: $e');
    }
  }

  // 关闭串口
  Future<void> _closePort() async {
    _readTimer?.cancel();

    if (_serialPort != null) {
      try {
        await _serialPort!.close();
        _showMessage('串口已断开');
      } catch (e) {
        _showMessage('断开串口时出错: $e');
      }
      _serialPort = null;
    }

    setState(() {
      _isConnected = false;
    });
  }

  // 发送数据
  Future<void> _sendData() async {
    if (_serialPort == null || !_isConnected) {
      _showMessage('串口未连接');
      return;
    }

    final text = _inputController.text;
    if (text.isEmpty) {
      _showMessage('请输入要发送的数据');
      return;
    }

    try {
      // 将字符串转换为字节数组
      final data = text.codeUnits;
      await _serialPort!.write(output: data);

      // 在输出框中显示发送的数据
      _appendToOutput('发送: $text\n');

      // 清空输入框
      _inputController.clear();

      _showMessage('数据发送成功');
    } catch (e) {
      _showMessage('发送数据失败: $e');
    }
  }

  // 开始读取数据
  void _startReading() {
    _readTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (_serialPort == null || !_isConnected) {
        timer.cancel();
        return;
      }

      try {
        final data = await _serialPort!.read();
        if (data.isNotEmpty) {
          // 将字节数组转换为字符串
          final text = String.fromCharCodes(data);
          _appendToOutput('接收: $text\n');
        }
      } catch (e) {
        // 如果读取出错，可能是连接断开了
        if (e.toString().contains('not open') || e.toString().contains('NotConnected')) {
          setState(() {
            _isConnected = false;
          });
          timer.cancel();
          _showMessage('串口连接已断开');
        }
      }
    });
  }

  // 向输出框添加文本
  void _appendToOutput(String text) {
    setState(() {
      _outputController.text += text;
    });

    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_outputController.text.isNotEmpty) {
        _outputController.selection = TextSelection.fromPosition(TextPosition(offset: _outputController.text.length));
      }
    });
  }

  // 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), duration: const Duration(seconds: 2)));
  }
}
