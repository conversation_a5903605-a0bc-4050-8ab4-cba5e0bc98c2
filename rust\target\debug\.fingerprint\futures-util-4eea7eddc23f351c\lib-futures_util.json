{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 6953988541840603879, "profile": 2241668132362809309, "path": 8693366195862988595, "deps": [[1615478164327904835, "pin_utils", false, 10134020113130807145], [4119161710677519185, "memchr", false, 16407184898666349223], [5700553729601021974, "build_script_build", false, 9279639071317292789], [6323537352664534935, "futures_io", false, 4460292726766211121], [6955678925937229351, "slab", false, 13775355554516553834], [7743984271493053878, "futures_core", false, 100805771017997474], [9961102183257020711, "futures_task", false, 7472454101070899931], [13790135811457663478, "futures_macro", false, 10765697147973830226], [16078276589449426613, "futures_channel", false, 7313445933960344600], [18183901555165856195, "futures_sink", false, 16222476913460808349], [18307711753340005737, "pin_project_lite", false, 16891582809222198337]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-4eea7eddc23f351c\\dep-lib-futures_util", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}