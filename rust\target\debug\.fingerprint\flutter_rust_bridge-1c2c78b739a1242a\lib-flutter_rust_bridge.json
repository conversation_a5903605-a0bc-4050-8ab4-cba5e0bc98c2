{"rustc": 1842507548689473721, "features": "[\"anyhow\", \"console_error_panic_hook\", \"dart-opaque\", \"default\", \"log\", \"portable-atomic\", \"rust-async\", \"thread-pool\", \"user-utils\", \"wasm-start\"]", "declared_features": "[\"anyhow\", \"backtrace\", \"chrono\", \"console_error_panic_hook\", \"dart-opaque\", \"default\", \"log\", \"portable-atomic\", \"rust-async\", \"thread-pool\", \"user-utils\", \"uuid\", \"wasm-start\"]", "target": 9045615548121237704, "profile": 11730204980851078290, "path": 7533777311547253285, "deps": [[1510982600281540773, "allo_isolate", false, 4410098750698757904], [1575878257545642546, "log", false, 13781920518740089289], [3216452410829326882, "dart_sys", false, 11147971350573869405], [3556178808812057340, "anyhow", false, 17797326036494344747], [3712811570531045576, "byteorder", false, 12975717303789611870], [3958489542916937055, "portable_atomic", false, 7101486205183057583], [4621265092863320678, "futures", false, 3552898232511202431], [9045754397332874331, "lazy_static", false, 8890928046786784734], [9938583016855828308, "tokio", false, 14321105033675457159], [11204906226375074320, "delegate_attr", false, 15450818064045347140], [14521117738091886193, "threadpool", false, 1315036522316385916], [16541086609288359095, "flutter_rust_bridge_macros", false, 3066598372918102204], [17705238635785893858, "build_script_build", false, 2897763701899234363]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flutter_rust_bridge-1c2c78b739a1242a\\dep-lib-flutter_rust_bridge", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}