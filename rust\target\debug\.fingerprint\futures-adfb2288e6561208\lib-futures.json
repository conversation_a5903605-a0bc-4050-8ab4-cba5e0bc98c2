{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 6120535526081445694, "profile": 2241668132362809309, "path": 17920806599311208807, "deps": [[5700553729601021974, "futures_util", false, 15423914043409664701], [6323537352664534935, "futures_io", false, 4460292726766211121], [7743984271493053878, "futures_core", false, 100805771017997474], [9961102183257020711, "futures_task", false, 7472454101070899931], [16078276589449426613, "futures_channel", false, 7313445933960344600], [18183901555165856195, "futures_sink", false, 16222476913460808349], [18216287767803176676, "futures_executor", false, 1136585413202452140]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-adfb2288e6561208\\dep-lib-futures", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}