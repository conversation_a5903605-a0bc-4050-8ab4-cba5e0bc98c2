{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 17212183120498275042, "deps": [[3060637413840920116, "proc_macro2", false, 6356398401606995572], [10418434610764581512, "unicode_ident", false, 13211797086964213862], [17990358020177143287, "quote", false, 9906207562101291133]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-0fec3b3562d6e06a\\dep-lib-syn", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}