{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-sink\", \"sink\", \"std\"]", "declared_features": "[\"alloc\", \"cfg-target-has-atomic\", \"default\", \"futures-sink\", \"sink\", \"std\", \"unstable\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 1724323575047656639, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-channel-af7036af4e262552\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": ["--cfg", "frb_expand"], "config": 2069994364910194474, "compile_kind": 0}